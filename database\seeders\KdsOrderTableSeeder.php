<?php

namespace Database\Seeders;

use App\Enums\Source;
use App\Models\Order;
use App\Enums\TaxType;
use App\Enums\OrderType;
use App\Models\OrderItem;
use App\Enums\OrderStatus;
use App\Enums\PosPaymentMethod;
use Illuminate\Database\Seeder;
use <PERSON><PERSON><PERSON><PERSON>\EnvEditor\EnvEditor;

class KdsOrderTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $envService = new EnvEditor();
        if ($envService->getValue('DEMO')) {
            Order::insert([
                [
                    'order_serial_no'    => date('dmy') . '3',
                    'token'              => "0103",
                    'user_id'            => 3,
                    'branch_id'          => 1,
                    'subtotal'           => 8.500000,
                    'discount'           => 0.000000,
                    'delivery_charge'    => 0.364328,
                    'total_tax'          => 0.000000,
                    'total'              => 8.500000,
                    'order_type'         => OrderType::TAKEAWAY,
                    'pos_payment_method' => PosPaymentMethod::CASH,
                    'order_datetime'     => now(),
                    'delivery_time'      => '20:30 - 21:00',
                    'preparation_time'   => 30,
                    'is_advance_order'   => 10,
                    'payment_method'     => 1,
                    'payment_status'     => 5,
                    'status'             => OrderStatus::PREPARING,
                    'dining_table_id'    => null,
                    'delivery_boy_id'    => null,
                    'reason'             => null,
                    'source'             => Source::POS,
                    'created_at'         => now(),
                    'updated_at'         => now()
                ],
                [
                    'order_serial_no'    => date('dmy') . '4',
                    'token'              => "0104",
                    'user_id'            => 2,
                    'branch_id'          => 1,
                    'subtotal'           => 18.730000,
                    'discount'           => 0.000000,
                    'delivery_charge'    => 0.000000,
                    'total_tax'          => 0.425000,
                    'total'              => 18.730000,
                    'order_type'         => OrderType::TAKEAWAY,
                    'pos_payment_method' => PosPaymentMethod::CASH,
                    'order_datetime'     => now(),
                    'delivery_time'      => '20:30 - 21:00',
                    'preparation_time'   => 30,
                    'is_advance_order'   => 10,
                    'payment_method'     => 1,
                    'payment_status'     => 5,
                    'status'             => OrderStatus::PREPARED,
                    'dining_table_id'    => null,
                    'delivery_boy_id'    => null,
                    'reason'             => null,
                    'source'             => Source::POS,
                    'created_at'         => now(),
                    'updated_at'         => now()
                ],
                [
                    'order_serial_no'    => date('dmy') . '5',
                    'token'              => "0105",
                    'user_id'            => 3,
                    'branch_id'          => 1,
                    'subtotal'           => 8.500000,
                    'discount'           => 0.000000,
                    'delivery_charge'    => 0.000000,
                    'total_tax'          => 0.000000,
                    'total'              => 8.500000,
                    'order_type'         => OrderType::DINING_TABLE,
                    'pos_payment_method' => PosPaymentMethod::CASH,
                    'order_datetime'     => now(),
                    'delivery_time'      => '20:30 - 21:00',
                    'preparation_time'   => 30,
                    'is_advance_order'   => 10,
                    'payment_method'     => 1,
                    'payment_status'     => 5,
                    'status'             => OrderStatus::PREPARED,
                    'dining_table_id'    => 1,
                    'delivery_boy_id'    => null,
                    'reason'             => null,
                    'source'             => Source::POS,
                    'created_at'         => now(),
                    'updated_at'         => now()
                ],
            ]);

            OrderItem::insert([
                [
                    'order_id'             => 3,
                    'branch_id'            => 1,
                    'item_id'              => 14,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 2.500000,
                    'item_variations'      => '[{"id":25,"item_id":14,"item_attribute_id":"1","variation_name":"Size","name":"Regular"}]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 2.500000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 3,
                    'branch_id'            => 1,
                    'item_id'              => 53,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 2.000000,
                    'item_variations'      => '[]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 2.000000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 3,
                    'branch_id'            => 1,
                    'item_id'              => 44,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 1.000000,
                    'item_variations'      => '[{"id":80,"item_id":44,"item_attribute_id":"1","variation_name":"Size","name":"Regular"}]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 1.000000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 3,
                    'branch_id'            => 1,
                    'item_id'              => 43,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 1.500000,
                    'item_variations'      => '[]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 1.500000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 3,
                    'branch_id'            => 1,
                    'item_id'              => 45,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 1.500000,
                    'item_variations'      => '[]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 1.500000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 4,
                    'branch_id'            => 1,
                    'item_id'              => 6,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 5.230000,
                    'item_variations'      => '[{"id":9,"item_id":6,"item_attribute_id":"1","variation_name":"Size","name":"Regular"}]',
                    'item_extras'          => '[{"id":3,"item_id":6,"name":"Add Onion"},{"id":4,"item_id":6,"name":"Add Patty"}]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 1.500000,
                    'total_price'          => 6.730000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 4,
                    'branch_id'            => 1,
                    'item_id'              => 48,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 1.500000,
                    'item_variations'      => '[]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 1.500000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 4,
                    'branch_id'            => 1,
                    'item_id'              => 18,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'VAT',
                    'tax_rate'             => 5,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.225000,
                    'price'                => 3.500000,
                    'item_variations'      => '[{"id":34,"item_id":18,"item_attribute_id":"1","variation_name":"Size","name":"Large - 10"}]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 1.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 4.500000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 4,
                    'branch_id'            => 1,
                    'item_id'              => 53,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 2.000000,
                    'item_variations'      => '[]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 2.000000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 4,
                    'branch_id'            => 1,
                    'item_id'              => 33,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'VAT',
                    'tax_rate'             => 5,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.200000,
                    'price'                => 4.000000,
                    'item_variations'      => '[{"id":76,"item_id":33,"item_attribute_id":"2","variation_name":"Quantity Choice","name":"Pack of 6"}]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 4.000000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 5,
                    'branch_id'            => 1,
                    'item_id'              => 14,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 2.500000,
                    'item_variations'      => '[{"id":25,"item_id":14,"item_attribute_id":"1","variation_name":"Size","name":"Regular"}]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 2.500000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 5,
                    'branch_id'            => 1,
                    'item_id'              => 53,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 2.000000,
                    'item_variations'      => '[]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 2.000000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 5,
                    'branch_id'            => 1,
                    'item_id'              => 44,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 1.000000,
                    'item_variations'      => '[{"id":80,"item_id":44,"item_attribute_id":"1","variation_name":"Size","name":"Regular"}]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 1.000000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 5,
                    'branch_id'            => 1,
                    'item_id'              => 43,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 1.500000,
                    'item_variations'      => '[]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 1.500000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
                [
                    'order_id'             => 5,
                    'branch_id'            => 1,
                    'item_id'              => 45,
                    'quantity'             => 1,
                    'discount'             => 0.000000,
                    'tax_name'             => 'NO-VAT',
                    'tax_rate'             => 0,
                    'tax_type'             => TaxType::PERCENTAGE,
                    'tax_amount'           => 0.000000,
                    'price'                => 1.500000,
                    'item_variations'      => '[]',
                    'item_extras'          => '[]',
                    'item_variation_total' => 0.000000,
                    'item_extra_total'     => 0.000000,
                    'total_price'          => 1.500000,
                    'instruction'          => '',
                    'created_at'           => now(),
                    'updated_at'           => now()
                ],
            ]);
        }
    }
}