<?php

namespace Database\Seeders;

use App\Enums\Ask;
use App\Models\Address;
use App\Enums\Role as EnumRole;
use <PERSON><PERSON><PERSON>lder\EnvEditor\EnvEditor;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Enums\Status;


class UserTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $envService = new EnvEditor();
        $admin      = User::create([
            'name'              => '<PERSON> Do<PERSON>',
            'email'             => '<EMAIL>',
            'phone'             => '1254875855',
            'username'          => 'admin',
            'email_verified_at' => now(),
            'password'          => bcrypt('123456'),
            'branch_id'         => 0,
            'status'            => Status::ACTIVE,
            'country_code'      => '+880',
            'is_guest'          => Ask::NO
        ]);
        $admin->assignRole(EnumRole::ADMIN);

        if ($envService->getValue('DEMO')) {
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 10',
                'latitude'  => '23.8069',
                'longitude' => '90.3687',
                'user_id'   => $admin->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 1',
                'latitude'  => '23.7956',
                'longitude' => '90.3537',
                'user_id'   => $admin->id,
            ]);
        }

        $customer = User::create([
            'name'              => 'Walking Customer',
            'email'             => '<EMAIL>',
            'phone'             => '1254444555',
            'username'          => 'default-customer',
            'email_verified_at' => now(),
            'password'          => bcrypt('123456'),
            'branch_id'         => 0,
            'status'            => Status::ACTIVE,
            'country_code'      => '+880',
            'is_guest'          => Ask::NO
        ]);
        $customer->assignRole(EnumRole::CUSTOMER);

        if ($envService->getValue('DEMO')) {
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 2',
                'latitude'  => '23.7873',
                'longitude' => '90.3514',
                'user_id'   => $customer->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Gulshan 2',
                'latitude'  => '23.7948',
                'longitude' => '90.4143',
                'user_id'   => $customer->id,
            ]);
        }

        if ($envService->getValue('DEMO')) {
            $customerOne = User::create([
                'name'              => 'Will Smith',
                'email'             => '<EMAIL>',
                'phone'             => '1253333444',
                'username'          => 'will-smith',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 0,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $customerOne->assignRole(EnumRole::CUSTOMER);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Gulshan 2',
                'latitude'  => '23.7948',
                'longitude' => '90.4143',
                'user_id'   => $customerOne->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 2',
                'latitude'  => '23.7873',
                'longitude' => '90.3514',
                'user_id'   => $customerOne->id,
            ]);
            $employeeOne = User::create([
                'name'              => 'Kiron Khan',
                'email'             => '<EMAIL>',
                'phone'             => '1275333453',
                'username'          => 'kiron-khan1313',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 1,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $employeeOne->assignRole(EnumRole::BRANCH_MANAGER);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 2',
                'latitude'  => '23.7873',
                'longitude' => '90.3514',
                'user_id'   => $employeeOne->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 1',
                'latitude'  => '23.7956',
                'longitude' => '90.3537',
                'user_id'   => $employeeOne->id,
            ]);

            $employeeTwo = User::create([
                'name'              => 'Shohag Ali',
                'email'             => '<EMAIL>',
                'phone'             => '1257654433',
                'username'          => 'shohag-ali3324',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 2,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $employeeTwo->assignRole(EnumRole::BRANCH_MANAGER);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Gulshan 2',
                'latitude'  => '23.7948',
                'longitude' => '90.4143',
                'user_id'   => $employeeTwo->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Gulshan 1',
                'latitude'  => '23.7821',
                'longitude' => '90.4161',
                'user_id'   => $employeeTwo->id,
            ]);

            $posOperatorOne = User::create([
                'name'              => 'Farha Israt ',
                'email'             => '<EMAIL>',
                'phone'             => '156873641',
                'username'          => 'farha-istat343',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 1,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $posOperatorOne->assignRole(EnumRole::POS_OPERATOR);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 2',
                'latitude'  => '23.7873',
                'longitude' => '90.3514',
                'user_id'   => $posOperatorOne->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 1',
                'latitude'  => '23.7956',
                'longitude' => '90.3537',
                'user_id'   => $posOperatorOne->id,
            ]);

            $posOperatorTwo = User::create([
                'name'              => 'Sahataz Afnan',
                'email'             => '<EMAIL>',
                'phone'             => '1249955570',
                'username'          => 'sahataz-afnan232',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 2,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $posOperatorTwo->assignRole(EnumRole::POS_OPERATOR);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Gulshan 2',
                'latitude'  => '23.7948',
                'longitude' => '90.4143',
                'user_id'   => $posOperatorTwo->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Gulshan 1',
                'latitude'  => '23.7821',
                'longitude' => '90.4161',
                'user_id'   => $posOperatorTwo->id,
            ]);

            $stuffOne = User::create([
                'name'              => 'Rohim Miya',
                'email'             => '<EMAIL>',
                'phone'             => '1222224443',
                'username'          => 'rohim-miya768',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 1,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $stuffOne->assignRole(EnumRole::STUFF);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 2',
                'latitude'  => '23.7873',
                'longitude' => '90.3514',
                'user_id'   => $stuffOne->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 1',
                'latitude'  => '23.7956',
                'longitude' => '90.3537',
                'user_id'   => $stuffOne->id,
            ]);

            $stuffTwo = User::create([
                'name'              => 'Kala Chan',
                'email'             => '<EMAIL>',
                'phone'             => '1238426043',
                'username'          => 'kala-chan890',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 2,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $stuffTwo->assignRole(EnumRole::STUFF);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Gulshan 2',
                'latitude'  => '23.7948',
                'longitude' => '90.4143',
                'user_id'   => $stuffTwo->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Gulshan 1',
                'latitude'  => '23.7821',
                'longitude' => '90.4161',
                'user_id'   => $stuffTwo->id,
            ]);
            $waiter = User::create([
                'name'              => 'Sakib Duronto',
                'email'             => '<EMAIL>',
                'phone'             => '1275333452',
                'username'          => 'sakib-duronto',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 1,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $waiter->assignRole(EnumRole::WAITER);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 2',
                'latitude'  => '23.7873',
                'longitude' => '90.3514',
                'user_id'   => $waiter->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 1',
                'latitude'  => '23.7956',
                'longitude' => '90.3537',
                'user_id'   => $waiter->id,
            ]);

            $chef = User::create([
                'name'              => 'Maruf Khan',
                'email'             => '<EMAIL>',
                'phone'             => '1275323453',
                'username'          => 'maruf-khan',
                'email_verified_at' => now(),
                'password'          => bcrypt('123456'),
                'branch_id'         => 1,
                'status'            => Status::ACTIVE,
                'country_code'      => '+880',
                'is_guest'          => Ask::NO
            ]);
            $chef->assignRole(EnumRole::CHEF);
            Address::create([
                'label'     => 'Home',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 2',
                'latitude'  => '23.7873',
                'longitude' => '90.3514',
                'user_id'   => $chef->id,
            ]);
            Address::create([
                'label'     => 'Work',
                'address'   => 'Dhaka Bangladesh',
                'apartment' => rand(0, 999) . ', Mirpur 1',
                'latitude'  => '23.7956',
                'longitude' => '90.3537',
                'user_id'   => $chef->id,
            ]);
        }
    }
}