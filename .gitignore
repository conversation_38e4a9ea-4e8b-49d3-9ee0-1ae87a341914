/node_modules
/public/hot
/public/storage
/storage/*.key
/vendor
.env.backup
.phpunit.result.cache
docker-compose.override.yml
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/.idea
/.vscode
.DS_Store
.DS_Store/*
/.DS_Store
storage/debugbar
/public/js/*
/public/mix-manifest.json
/public/js/app.js
public/js/app.js

public/js
public/js/*
package-lock.json
_ide_helper.php
_ide_helper_models.php
storage/media-library/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 
