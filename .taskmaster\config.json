{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro-preview-05-06", "maxTokens": 1048000, "temperature": 0.2}, "research": {"provider": "gemini-cli", "modelId": "gemini-2.5-pro", "maxTokens": 65536, "temperature": 0.1}, "fallback": {"provider": "gemini-cli", "modelId": "gemini-2.5-flash", "maxTokens": 65536, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "English", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {}}